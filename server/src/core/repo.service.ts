import { Injectable, Logger } from '@nestjs/common';
import { execSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

/**
 * 仓库信息接口
 */
export interface RepoInfo {
  name: string;
  path: string;
  gitUrl: string;
  currentBranch: string;
  currentCommit: string;
  lastModified: Date;
  isValid: boolean;
}

/**
 * 仓库管理服务 - 管理 repos 目录下的仓库
 */
@Injectable()
export class RepoService {
  private readonly logger = new Logger(RepoService.name);
  private readonly reposDir: string;
  private cachedRepos: RepoInfo[] = [];
  private lastScanTime: number = 0;
  private readonly CACHE_TTL = 30000; // 30秒缓存

  constructor() {
    this.reposDir = path.resolve(process.cwd(), '../repos');
    this.ensureReposDirectory();
  }

  /**
   * 确保 repos 目录存在
   */
  private ensureReposDirectory() {
    if (!fs.existsSync(this.reposDir)) {
      fs.mkdirSync(this.reposDir, { recursive: true });
      this.logger.log(`创建 repos 目录: ${this.reposDir}`);
    }
  }

  /**
   * 获取所有仓库列表
   */
  async getRepositories(): Promise<RepoInfo[]> {
    const now = Date.now();
    
    // 如果缓存有效，直接返回缓存
    if (this.cachedRepos.length > 0 && (now - this.lastScanTime) < this.CACHE_TTL) {
      return this.cachedRepos;
    }

    this.logger.log('扫描 repos 目录...');
    const repos: RepoInfo[] = [];

    try {
      const entries = fs.readdirSync(this.reposDir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith('.')) {
          const repoPath = path.join(this.reposDir, entry.name);
          const repoInfo = await this.getRepoInfo(entry.name, repoPath);
          
          if (repoInfo) {
            repos.push(repoInfo);
          }
        }
      }

      this.cachedRepos = repos;
      this.lastScanTime = now;
      
      this.logger.log(`发现 ${repos.length} 个仓库`);
      
    } catch (error) {
      this.logger.error('扫描仓库目录失败:', error);
    }

    return repos;
  }

  /**
   * 获取单个仓库信息
   */
  private async getRepoInfo(name: string, repoPath: string): Promise<RepoInfo | null> {
    try {
      // 检查是否是 Git 仓库
      if (!this.isGitRepository(repoPath)) {
        this.logger.warn(`${name} 不是有效的 Git 仓库`);
        return null;
      }

      const originalCwd = process.cwd();
      
      try {
        process.chdir(repoPath);
        
        // 获取 Git 信息
        const gitUrl = this.getRemoteOriginUrl();
        const currentBranch = this.getCurrentBranch();
        const currentCommit = this.getCurrentCommit();
        
        // 获取最后修改时间
        const stats = fs.statSync(repoPath);
        
        return {
          name,
          path: repoPath,
          gitUrl,
          currentBranch,
          currentCommit,
          lastModified: stats.mtime,
          isValid: true
        };
        
      } finally {
        process.chdir(originalCwd);
      }
      
    } catch (error) {
      this.logger.error(`获取仓库信息失败 ${name}:`, error);
      return {
        name,
        path: repoPath,
        gitUrl: '',
        currentBranch: '',
        currentCommit: '',
        lastModified: new Date(),
        isValid: false
      };
    }
  }

  /**
   * 根据名称获取仓库信息
   */
  async getRepositoryByName(name: string): Promise<RepoInfo | null> {
    const repos = await this.getRepositories();
    return repos.find(repo => repo.name === name) || null;
  }

  /**
   * 检查是否是 Git 仓库
   */
  private isGitRepository(repoPath: string): boolean {
    const gitDir = path.join(repoPath, '.git');
    return fs.existsSync(gitDir);
  }

  /**
   * 获取远程仓库 URL
   */
  private getRemoteOriginUrl(): string {
    try {
      return execSync('git config --get remote.origin.url', { encoding: 'utf8' }).trim();
    } catch (error) {
      return '';
    }
  }

  /**
   * 获取当前分支
   */
  private getCurrentBranch(): string {
    try {
      return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return '';
    }
  }

  /**
   * 获取当前提交
   */
  private getCurrentCommit(): string {
    try {
      return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return '';
    }
  }

  /**
   * 刷新缓存
   */
  async refreshCache(): Promise<void> {
    this.cachedRepos = [];
    this.lastScanTime = 0;
    await this.getRepositories();
  }

  /**
   * 获取仓库统计信息
   */
  async getRepositoryStats(): Promise<{
    total: number;
    valid: number;
    invalid: number;
    lastScanTime: Date;
  }> {
    const repos = await this.getRepositories();
    const valid = repos.filter(repo => repo.isValid);
    const invalid = repos.filter(repo => !repo.isValid);

    return {
      total: repos.length,
      valid: valid.length,
      invalid: invalid.length,
      lastScanTime: new Date(this.lastScanTime)
    };
  }

  /**
   * 删除仓库
   */
  async deleteRepository(name: string): Promise<boolean> {
    try {
      const repoPath = path.join(this.reposDir, name);
      
      if (!fs.existsSync(repoPath)) {
        this.logger.warn(`仓库不存在: ${name}`);
        return false;
      }

      // 删除目录
      fs.rmSync(repoPath, { recursive: true, force: true });
      
      // 刷新缓存
      await this.refreshCache();
      
      this.logger.log(`删除仓库: ${name}`);
      return true;
      
    } catch (error) {
      this.logger.error(`删除仓库失败 ${name}:`, error);
      return false;
    }
  }
}
