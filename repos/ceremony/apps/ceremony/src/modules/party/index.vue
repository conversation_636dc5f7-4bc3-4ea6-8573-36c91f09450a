<template>
    <!-- 组件代码都在这里判断渠道 tab，原则上不在其他业务组件里判断渠道 tab -->
    <!-- # if (target === '4tab') -->
    <PageHeader4Tab ref="pageHeader4TabRef" />
    <!-- # endif -->
    <PageAdaptor
        class="pb-[60px]"
        :class="{
            'mt-[-40px]': isSearchTab || is4Tab,
            'fix-height': isSearchTab || is4Tab,
        }"
    >
        <!-- 刷新按钮 -->
        <!-- # if (target === '4tab') -->
        <RefreshIcon :height="refreshIconHeight" @refresh="reloadPage()" />
        <!-- # endif -->
        <!-- 主 K 区域 -->
        <Kv4Tab
            :img-src="conf4Tab?.kv?.imgUrl"
            :video-src="conf4Tab?.kv?.videoUrl"
        />
        <!-- 红包飞入icon目标点 -->
        <!-- <div id="record" class="poi-fly"></div> -->
        <AElseStatus
            v-if="!pageStatus.success"
            class="else-status"
            :page-status="pageStatus"
            :is-show-refresh="pageStatus.error"
            :no-data="pageStatus.nodata"
            @refresh="refreshAll"
        />
        <template v-else>
            <div class="content-container">
                <!-- 直播流卡片 -->
                <VideoLive
                    v-if="
                        homeData?.showHeadLiveStream &&
                        homeData?.headLiveStreamData
                    "
                    class="order-0"
                    :live-data="homeData.headLiveStreamData"
                    :show-live-stream="homeData.showHeadLiveStreamThreshold"
                    @refresh="() => refreshAll({ delay: true })"
                />
                <!-- 新春锦鲤 -->
                <!-- <PartyKoi
                    v-if="
                        homeData?.showKoi &&
                        homeData?.koiData &&
                        homeData?.koiActivityId
                    "
                    :class="{
                        'order-2 mt-[10px] mb-[-35px]':
                            homeData.showHeadLiveStream,
                    }"
                    class="mb-[-25px]"
                    :koi-data="homeData?.koiData"
                    :koi-activity-id="homeData.koiActivityId"
                /> -->
                <!-- 官号晚会-视频/直播轮播 -->
                <VideoSwiper
                    v-if="
                        !homeData?.showHeadLiveStream &&
                        homeData?.headPhotoFeedData
                    "
                    :class="{ 'mt-[14px]': !homeData?.showKoi }"
                    :home-data="swipperData.homeData"
                />
                <!-- 官号晚会-按钮 -->
                <PartyButtons
                    :class="
                        homeData.showHeadLiveStream
                            ? 'order-1 living'
                            : 'mt-[8px]'
                    "
                    :home-data="homeData"
                    @update="updateHomeData"
                    @refresh-all="() => refreshAll({ delay: true })"
                />
            </div>

            <!-- 做任务抽奖 -->
            <PartyLottery
                :lucky-bag-data="homeData?.luckyBagData"
                :audience-task-data="homeData?.audienceTaskData"
                :activity-id="homeData?.koiActivityId"
            />

            <!-- 视频卡 -->
            <!-- <VideoList
                v-if="homeData?.bottomFeed"
                :list="homeData.bottomFeed"
            /> -->

            <!-- 本地生活卡 -->
            <!-- <LocalLifeGoods
                v-if="homeData?.locallifeItemCardInfo"
                :list="homeData.locallifeItemCardInfo?.itemCards"
                :more-url="homeData.locallifeItemCardInfo?.moreItemJumpUrl"
            /> -->
            <component
                :is="item.component"
                v-for="(item, index) in componentsList"
                :key="index"
                v-bind="item.props"
                @refresh="refresh"
            ></component>
            <Back />
        </template>
    </PageAdaptor>
</template>

<script setup lang="ts">
import {
    computed,
    defineAsyncComponent,
    onMounted,
    ref,
    reactive,
    watch,
} from 'vue';
import { storeToRefs } from 'pinia';
// import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconf from '@pet/ones-use.useKconf/index';
import { isSearchTab, is4Tab } from '@pet/ones-use.is4Tab/index';
import VideoSwiper from '@pet/ones-ui.video-swiper/index.vue';
import VideoLive from '@pet/ones-ui.video-live/index.vue';
import { refreshPhoto } from '@pet/ones-ui.video-list/service/index';
import RefreshIcon from '@pet/ones-ui.party-refresh/index.vue';
import PartyButtons from '@pet/ones-ui.party-buttons/index.vue';
import PageHeader4Tab from '@pet/ones-ui.page-header-4tab/index.vue';
import Kv4Tab from '@pet/ones-ui.kv-4tab/index.vue';
import { sendFmp } from '@gundam/weblogger';
import { AElseStatus, PageAdaptor } from '@alive-ui/base';
import { hideLoadingPage, Report } from '@alive-ui/actions';
import type { refreshPhotoPayload } from '@pet/ones-ui.video-list/service/index';
// # if (target === '4tab')
// # endif
// import PartyKoi from '@pet/ones-ui.party-koi/index.vue';
// # if (target === '4tab')
// # endif
// eslint-disable-next-line import/order
import usePartyPageModel from './models/page';

// # if (target === '4tab')
// eslint-disable-next-line import/order
import { useNativeVisible } from '@pet/ones-use.xtabModule/index';

// 可见性注册
useNativeVisible();
// # endif

function reloadPage() {
    console.log('reloadPage----------');
    location.reload();
}

const { conf4Tab } = storeToRefs(useKconf());
// const moduleSwitch = usePartySwitch();

const pageHeader4TabRef = ref();

const refreshIconHeight = computed(
    () => pageHeader4TabRef.value?.headerHeight ?? 80,
);

const PartyLottery = defineAsyncComponent(() => {
    return import('@pet/ones-ui.party-lottery/index.vue');
});
const VideoList = defineAsyncComponent(() => {
    return import('@pet/ones-ui.video-list/index.vue');
});
const VideoListItem = defineAsyncComponent(() => {
    return import('@pet/ones-ui.video-list/video-item.vue');
});
const LocalLifeGoods = defineAsyncComponent(() => {
    return import('@pet/ones-ui.local-life-goods/index.vue');
});
const Back = defineAsyncComponent(() => {
    return import('@/components/back-exit/index.vue');
});

const partyPageModel = usePartyPageModel();
const { pageStatus, homeData } = storeToRefs(partyPageModel);
const { refreshAll, updateHomeData } = partyPageModel;
const refresh = async (params: refreshPhotoPayload) => {
    try {
        const res = await refreshPhoto(params);
        // 更新局部数据
        homeData.value.bottomFeed[params.photoArea - 1].photoFeeds =
            res.photoFeeds;
    } catch (error) {
        console.log(error);
        Report.biz.error('第四Tab视频卡片刷新失败', {
            error,
        });
    }
};
const componentsList = computed(() => {
    const config = {
        video1: {
            component: VideoListItem,
            props: {
                data: homeData.value?.bottomFeed?.[0]?.photoFeeds,
                type: 1,
                total: homeData.value?.bottomFeed?.[0]?.maxPage,
                title: homeData.value?.bottomFeed?.[0]?.title,
                subTitle: homeData.value?.bottomFeed?.[0]?.subTitle,
            },
        },
        video2: {
            component: VideoListItem,
            props: {
                data: homeData.value?.bottomFeed?.[1]?.photoFeeds,
                type: 2,
                total: homeData.value?.bottomFeed?.[1]?.maxPage,
                title: homeData.value?.bottomFeed?.[1]?.title,
                subTitle: homeData.value?.bottomFeed?.[1]?.subTitle,
            },
        },
        localCard: {
            component: LocalLifeGoods,
            props: {
                list: homeData.value?.localLifeItemCardInfo?.itemCards,
                moreUrl: homeData.value?.localLifeItemCardInfo?.moreItemJumpUrl,
            },
        },
    };
    const arr = [];
    for (const key of conf4Tab.value?.cardOrders) {
        arr.push(config[key as 'video1' | 'video2' | 'localCard']);
    }
    return arr;
});

refreshAll().finally(() => {
    console.log('refreshAll----------');

    sendFmp();
    if (is4Tab) {
        hideLoadingPage().catch(console.error);
    }
});

/**
 * 更新 swipper 组件属性, 会导致组件闪跳。
 * 因此不直接把 homeData.headPhotoFeedData 传给 swipper 组件（否则每次 home 接口更新都会触发闪跳）
 * 而是传下面的属性, homeData 更新后做一些判断, 非必要, 就不更新 swipperData 了。
 */
const swipperData = reactive({
    homeData: {
        headPhotoFeedData: {
            photoFeeds: [] as PhotoFeed[],
        },
        dataVersion: '',
    },
});

type PhotoFeed = {
    photoId: number;
    authorId: number;
    viewCount: string;
    title: string;
    cover: string;
    playUrl: string;
};

// homeData 变动时, 更新 swipperHomeData
watch(() => partyPageModel.homeData, onHomeDataUpdate, { immediate: true });

// home 接口更新后调用
function onHomeDataUpdate() {
    const oldPhotoFeeds: PhotoFeed[] =
        swipperData.homeData.headPhotoFeedData.photoFeeds || [];
    const newPhotoFeeds: PhotoFeed[] =
        homeData.value?.headPhotoFeedData?.photoFeeds || [];

    // 数据是否有变动
    let hasChange: boolean;

    hasChange =
        swipperData.homeData.dataVersion !== homeData.value?.dataVersion;

    if (!hasChange) {
        // 版本相同或忘记配置的情况下, 比较 photoId 是否相等
        if (oldPhotoFeeds.length !== newPhotoFeeds.length) {
            hasChange = true;
        } else if (
            newPhotoFeeds.some(
                (newItem, ind) =>
                    newItem.photoId !== oldPhotoFeeds[ind]?.photoId,
            )
        ) {
            hasChange = true;
        }
    }

    // 有变动才更新
    if (hasChange) {
        swipperData.homeData.headPhotoFeedData.photoFeeds = newPhotoFeeds;
        swipperData.homeData.dataVersion = homeData.value?.dataVersion;
    }
}
</script>

<style lang="less" scoped>
.content-container {
    display: flex;
    flex-direction: column;
}

.living {
    z-index: 1;
    margin-top: -118px;
    margin-bottom: 20px;
}

.poi-fly {
    position: absolute;
    top: 0;
    right: 0;
}

.else-status {
    width: 100%;
    margin: 50% 0;
}

.fix-height {
    min-height: calc(100vh + 40px);
}
</style>
