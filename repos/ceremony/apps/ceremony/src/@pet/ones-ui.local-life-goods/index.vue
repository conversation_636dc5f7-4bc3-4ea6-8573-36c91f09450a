<template>
    <div v-if="isShow" class="local-life">
        <ACard>
            <ACardTitle> {{ conf4Tab?.localLifeCard?.title }} </ACardTitle>
            <ACardSubtitle>
                {{ conf4Tab?.localLifeCard?.subTitle }}
            </ACardSubtitle>
            <ACardContent class="local-life__content">
                <div class="goods-list p-12px flex flex-wrap flex-center">
                    <div
                        v-for="item in list"
                        :key="item.id"
                        v-show-log="{
                            action: 'OP_ACTIVITY_COMMODITY_CARD',
                            params: {
                                id: item.id,
                            },
                        }"
                        v-click-log="{
                            action: 'OP_ACTIVITY_COMMODITY_CARD',
                            params: {
                                id: item.id,
                            },
                        }"
                        class="goods-item"
                        @click="goDetailItem(item.itemDetailUrl)"
                    >
                        <div
                            class="pic"
                            :style="`background-image: url(${item.imgUrl})`"
                        ></div>
                        <div class="goods-title">
                            <img
                                v-if="item.tagImgUrl"
                                class="tag-img"
                                :src="item.tagImgUrl"
                            />
                            {{ item.title }}
                        </div>
                        <div class="price-box">
                            <div class="origin-price">
                                ¥{{ item.marketPrice }}
                            </div>
                            <div class="discount-price">
                                {{ item.discountPrice }}
                            </div>
                            <div class="price">
                                <span>{{ item.couponPrice }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-if="moreUrl"
                    class="a-text-main flex-center text-13px"
                    @click="goDetail"
                >
                    查看更多优惠
                    <Right class="a-text-main" />
                </div>
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import useKconf from '@pet/ones-use.useKconf/index';
import { Toast } from '@lux/sharp-ui-next';
import { Right } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import {
    bolIsAuthor,
    isYodaPCContainer,
    isOutLiveRoom,
} from '@alive-ui/actions';
import type { GoodsItem } from './schema/index';

const { conf4Tab } = storeToRefs(useKconf());

const props = defineProps<{
    list: GoodsItem[];
    title: string;
    subTitle: string;
    moreUrl: string;
}>();
const emit = defineEmits<{
    (e: 'refresh', params: any): void;
}>();

const goDetail = throttle(() => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    window.location.href = props.moreUrl;
}, 500);
const goDetailItem = throttle((kwaiurl: string) => {
    if (!kwaiurl) {
        return;
    }
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    window.location.href = kwaiurl;
}, 500);
const getImgStyle = (url: string) => {
    return `#000 url("${url}") center / 100% no-repeat`;
};

const isShow = computed(() => {
    // 间外且列表有数据才展示
    return props.list?.length > 0 && isOutLiveRoom;
});
</script>

<style lang="less" scoped>
@font-family-primary: PingFang SC;
@font-family-secondary: SF Pro;
@color-origin-price: #c6c6c6;
@color-discount-price: #ff4815;
@color-price: #fff;
@background-price-box: url('./assets/price-bg.png') center / 100% no-repeat;

.local-life {
    position: relative;
    :deep(.card-title-icon-attrs) {
        display: none;
    }

    &__content {
        position: relative;

        .goods-list {
            padding-top: 12px;
        }
        .goods-item {
            box-sizing: border-box;
            width: 114px;
            height: 207px;
            margin: 0 8px 8px 0;
            flex: 0 0 calc(33.33% - 8px);
            display: flex;
            flex-direction: column;
            background: rgba(255, 212, 156, 0.08);
            border-radius: 8px;
            position: relative;

            .pic {
                width: 100%;
                height: 114px;
                border-radius: 6px;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }

            .goods-title {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 13px;
                line-height: 18px;
                color: #fff;
                padding: 6px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                white-space: normal;

                .tag-img {
                    height: 14px;
                    width: auto;
                    display: inline-block;
                    line-height: 18px;
                    position: relative;
                    bottom: -2px;
                }
            }

            .desc {
                margin-top: 4px;
                width: 114px;
                font-size: 12px;
                line-height: 18px;
            }

            .price-box {
                background: @background-price-box;
                width: 100%;
                height: 51px;
                padding: 0 0px 8px 4px;
                position: absolute;
                bottom: 0;
                left: 0;

                .origin-price,
                .discount-price {
                    font-family: @font-family-primary;
                    font-weight: 500;
                    font-size: 10px;
                    line-height: 14px;
                }

                .origin-price {
                    text-decoration: line-through;
                    color: @color-origin-price;
                    margin-top: 6px;
                }

                .discount-price {
                    color: @color-discount-price;
                    margin-top: 9px;
                }

                .price {
                    font-family: @font-family-secondary;
                    font-weight: 590;
                    font-size: 16px;
                    line-height: 20px;
                    height: 20px;
                    letter-spacing: -1px;
                    color: @color-price;
                    width: 68px;
                    position: absolute;
                    right: 1px;
                    top: 6px;
                    text-align: center;

                    span {
                        position: relative;
                        padding: 0 9px;

                        &:before {
                            content: '¥';
                            font-family: @font-family-primary;
                            font-weight: 600;
                            font-size: 9px;
                            line-height: 9px;
                            color: @color-price;
                            position: absolute;
                            left: 0;
                            bottom: 4px;
                        }
                    }
                }
            }
            &:nth-child(n + 4) {
                margin-top: 12px;
            }

            &:nth-child(3n) {
                margin-right: 0;
            }
        }

        .take-more {
            margin-top: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .more-btn {
        background: linear-gradient(88.24deg, #ffe8be 1.42%, #fecfa4 100.84%);
        width: 358px;
        height: 36px;
        border-radius: 18px;
        margin: 0 auto;
        margin-top: 12px;
    }
}
</style>
